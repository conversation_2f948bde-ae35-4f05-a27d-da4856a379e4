import axios from 'axios'
import { defineStore } from 'pinia'

export const useTextToSpeechStore = defineStore('textToSpeechStore', {
  state: () => ({
    textToSpeechResult: null as any,
    aiToolSpeechCardRef: null as any,

    loadings: {
      textToSpeech: false
    } as Record<string, boolean>,

    errors: {
      textToSpeech: null
    } as Record<string, any>,

    inputText: '',
    selectedFiles: [] as File[],
    supportFiles: [
      'docx',
      'xlsx',
      'pptx',
      'pdf',
      'epub',
      'mobi',
      'txt',
      'html',
      'odt',
      'ods',
      'odp',
      'azw',
      'azw3',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'application/epub+zip',
      'application/x-mobipocket-ebook',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    supportFilesDisplay: [
      'DOCX',
      'XLSX',
      'PPTX',
      'PDF',
      'EPUB',
      'MOBI',
      'TXT',
      'HTML',
      'ODT',
      'ODS',
      'ODP',
      'AZW',
      'AZW3'
    ],
    uploadProgress: 0,
    showVoiceModal: false,

    userCustomPrompts: [] as any[],
    selectedPrompt: null as any,

    custom_prompt: ''
  }),

  getters: {
    hasSelectedFiles: state => state.selectedFiles.length > 0,
    maxTextLength: (): number => {
      const authStore = useAuthStore()
      if (authStore.isPremiumUser) {
        return 10000
      }
      return 500
    },
    inputTextLength: state => state.inputText?.length || 0
  },

  actions: {
    async textToSpeech(payload: {
      input: string
      model: string
      emotion?: string
      speed?: number
      output_format?: string
      voices?: any[]
      custom_prompt?: string
      vibe_id?: number
      accent?: string
      model_name?: string
      name?: string
    }) {
      const toast = useToast()

      try {
        this.loadings.textToSpeech = true
        this.errors.textToSpeech = null

        // Create request payload
        const requestData = {
          model: payload.model,
          voices: payload.voices || [],
          speed: payload.speed || 1,
          input: payload.input,
          output_format: payload.output_format || 'mp3',
          emotion: payload.emotion,
          custom_prompt: payload.custom_prompt,
          vibe_id: payload.vibe_id,
          accent: payload.accent,
          model_name: payload.model_name,
          name: payload.name
        }
        this.textToSpeechResult = {
          ...requestData,
          model_name: payload.model,
          input_text: payload.input,
          generated_audio: [
            {
              voices: payload.voices
            }
          ]
        }
        // Make the API call
        const { apiService } = useAPI()
        const response = await apiService.post('/text-to-speech', requestData)

        this.textToSpeechResult = {
          ...this.textToSpeechResult,
          ...response.data
        }

        // Start auto sync if we have a history UUID
        if (response.data.history_uuid) {
          const { startAutoSync } = useAutoSyncHistory()
          startAutoSync({
            uuid: response.data.history_uuid,
            intervalMs: 30000, // 30 seconds
            maxDurationMs: 300000, // 5 minutes
            targetStatuses: [2, 3], // Complete or Error
            onStatusChange: (status, historyDetail) => {
              console.log(`🚀 ~ Speech generation status update: ${status}`)
              // Update result with latest data
              this.textToSpeechResult = {
                ...this.textToSpeechResult,
                ...historyDetail
              }
            },
            onComplete: (historyDetail) => {
              console.log('🚀 ~ Speech generation completed:', historyDetail)
              this.textToSpeechResult = {
                ...this.textToSpeechResult,
                ...historyDetail
              }
            },
            onError: (error) => {
              console.error('🚀 ~ Speech generation sync error:', error)
            }
          })
        }

        return response
      } catch (error: any) {
        console.log('🚀 ~ textToSpeech error:', error)

        const { $i18n } = useNuxtApp()
        const t = $i18n.t
        // toast.add({
        //   id: 'speech-error',
        //   title: t('Error') || 'Error',
        //   description:
        //     error.response?.data?.detail
        //     || error.message
        //     || 'Speech generation failed. Please try again.',
        //   color: 'error'
        // })

        this.errors.textToSpeech = error
        return null
      } finally {
        this.loadings.textToSpeech = false
      }
    },

    clearResult() {
      this.textToSpeechResult = null
    },

    setPrompt(prompt: string) {
      this.inputText = prompt
    },

    async getUploadFileUrl(file: File) {
      const { apiService } = useAPI()
      const response = await apiService.get('/get-upload-file-url', {
        params: {
          file_name: file.name,
          file_size: file.size
        }
      })
      return response.data
    },

    async uploadFile(file: File, uploadUrl: string) {
      try {
        this.loadings['uploadFile'] = true
        this.errors['uploadFile'] = null

        const response = await axios.put(uploadUrl, file, {
          headers: {
            'Content-Type': file?.type || 'application/octet-stream'
          },
          onUploadProgress: (progressEvent: any) => {
            this.uploadProgress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            console.log(
              '🚀 ~ uploadFile ~ this.uploadProgress:',
              this.uploadProgress
            )
          }
        })
        return response
      } catch (error) {
        console.log('🚀 ~ uploadFile error:', error)
        this.errors['uploadFile'] = error
        return null
      } finally {
        this.loadings['uploadFile'] = false
      }
    },

    async documentToSpeech(
      file: File,
      payload: {
        input: string
        model: string
        speed?: number
        output_format?: string
        voices?: any[]
        custom_prompt?: string
        model_name?: string
        file_password?: string
      }
    ) {
      try {
        this.loadings['textToSpeech'] = true
        this.errors['textToSpeech'] = null
        this.textToSpeechResult = {
          ...payload,
          model_name: payload.model,
          input_text: file.name,
          generated_audio: [
            {
              voices: payload.voices
            }
          ]
        }
        const uploadUrlRes = await this.getUploadFileUrl(file)
        await this.uploadFile(file, uploadUrlRes.url)

        const requestData = {
          ...payload,
          s3_file_path: uploadUrlRes.s3_file_path,
          file_name_origin: uploadUrlRes.file_name_origin
        }

        const { apiService } = useAPI()
        const response = await apiService.post('/tts-document-s3', requestData)
        const historyStore = useHistoryStore()
        const historyDetail = await historyStore.fetchHistoryDetail(
          response.data.history_uuid
        )
        this.textToSpeechResult = {
          ...this.textToSpeechResult,
          ...historyDetail
        }

        // Start auto sync for document to speech
        if (response.data.history_uuid) {
          const { startAutoSync } = useAutoSyncHistory()
          startAutoSync({
            uuid: response.data.history_uuid,
            intervalMs: 30000, // 30 seconds
            maxDurationMs: 300000, // 5 minutes
            targetStatuses: [2, 3], // Complete or Error
            onStatusChange: (status, historyDetail) => {
              console.log(`🚀 ~ Document speech generation status update: ${status}`)
              // Update result with latest data
              this.textToSpeechResult = {
                ...this.textToSpeechResult,
                ...historyDetail
              }
            },
            onComplete: (historyDetail) => {
              console.log('🚀 ~ Document speech generation completed:', historyDetail)
              this.textToSpeechResult = {
                ...this.textToSpeechResult,
                ...historyDetail
              }
            },
            onError: (error) => {
              console.error('🚀 ~ Document speech generation sync error:', error)
            }
          })
        }

        return response
      } catch (error) {
        console.log('🚀 ~ textToSpeech error:', error)
        this.errors['textToSpeech'] = error
        return null
      } finally {
        this.loadings['textToSpeech'] = false
      }
    },

    async fetchPrompts() {
      // Check if user is authenticated and account is verified
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated || authStore.isNotVerifyAccount) {
        console.log('🚀 ~ fetchPrompts ~ User not authenticated or account not verified')
        return false
      }

      // if (this.userCustomPrompts.length > 0) return
      try {
        this.loadings['fetchPrompts'] = true
        this.errors['fetchPrompts'] = null

        const { apiService } = useAPI()
        const response = await apiService.get('/custom-prompts')
        const data = response.data

        if (data) {
          this.userCustomPrompts = data

          return true
        } else {
          throw new Error('Can\'t fetch prompts')
        }
      } catch (error: any) {
        console.log('🚀 ~ fetchPrompts ~ error:', error)
        this.errors['fetchPrompts'] = error
        return false
      } finally {
        this.loadings['fetchPrompts'] = false
      }
    },

    async updatePrompts(payload: any): Promise<boolean> {
      try {
        this.loadings['updatePrompts'] = true
        const { apiService } = useAPI()
        const response = await apiService.put('/custom-prompts', payload)
        const data = response.data
        if (data) {
          return true
        } else {
          throw new Error('Can\'t create speech')
        }
      } catch (error: any) {
        console.log('🚀 ~ updatePrompts ~ error:', error)
        return false
      } finally {
        this.loadings['updatePrompts'] = false
      }
    },

    async addNewPrompt(payload: any) {
      // Check if user is authenticated and account is verified
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated || authStore.isNotVerifyAccount) {
        console.log('🚀 ~ addNewPrompt ~ User not authenticated or account not verified')
        return false
      }

      try {
        this.loadings['addNewPrompt'] = true

        const { apiService } = useAPI()
        const response = await apiService.post('/custom-prompts', payload)
        const data = response.data

        if (data) {
          const newPrompt = {
            ...payload,
            id: data?.data?.id
          }
          this.userCustomPrompts.unshift(newPrompt)
          this.selectedPrompt = newPrompt
          return data.value
        }
      } catch (error: any) {
        console.log('🚀 ~ addNewPrompt ~ error:', error)
        this.errors['addNewPrompt'] = error
        return false
      } finally {
        this.loadings['addNewPrompt'] = false
      }
    },

    async removePrompt(prompt: any) {
      // Check if user is authenticated and account is verified
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated || authStore.isNotVerifyAccount) {
        console.log('🚀 ~ removePrompt ~ User not authenticated or account not verified')
        return false
      }

      try {
        this.loadings['removePrompt'] = true
        const { apiService } = useAPI()
        const response = await apiService.delete(
          '/custom-prompts/' + prompt?.id
        )
        const data = response.data
        if (data) {
          const index = this.userCustomPrompts.findIndex(
            e => e.id === prompt.id
          )
          this.userCustomPrompts.splice(index, 1)
        }

        return data
      } catch (error: any) {
        console.log('🚀 ~ removePrompt ~ error:', error)
        return false
      } finally {
        this.loadings['removePrompt'] = false
      }
    },

    async updatePrompt(prompt: any) {
      // Check if user is authenticated and account is verified
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated || authStore.isNotVerifyAccount) {
        console.log('🚀 ~ updatePrompt ~ User not authenticated or account not verified')
        return false
      }

      try {
        this.loadings['updatePrompt'] = true
        const { apiService } = useAPI()
        const response = await apiService.put(
          '/custom-prompts/' + prompt?.id,
          prompt
        )
        const data = response.data
        if (data) {
          this.userCustomPrompts = this.userCustomPrompts.map((e, i) => {
            if (e.id === prompt.id) {
              return prompt
            }
            return e
          })
        }
        return data
      } catch (error: any) {
        console.log('🚀 ~ updatePrompt ~ error:', error)
        return false
      } finally {
        this.loadings['updatePrompt'] = false
      }
    }
  }
})
