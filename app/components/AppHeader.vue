<script setup lang="ts">
import { en, vi, ja, zh_cn, es, fr, de, pt } from '@nuxt/ui/locale'
import { storeToRefs } from 'pinia'
import { watch } from 'vue'

const nuxtApp = useNuxtApp()
const { activeHeadings, updateHeadings } = useScrollspy()
const { t } = useI18n()
const appStore = useAppStore()
const { lastMenus, loading, locale, localeForI18n } = storeToRefs(appStore)
const router = useRouter()
const route = useRoute()

// Language and locale handling
const { locale: i18nLocale, setLocale } = useI18n()
watch(i18nLocale, (newLocale: string) => {
  // zh-CN -> zh_cn
  locale.value = newLocale
  setLocale(localeForI18n.value)
})
const items = computed(() => [
  {
    label: t('Home'),
    onClick: () => {
      router.push({
        name: lastMenus.value.app
      })
    },
    active: route.name?.toString().includes('app')
  },
  {
    label: t('History'),
    to: '/history'
  },
  {
    label: t('Pricing'),
    to: '/pricing'
  }
])

nuxtApp.hooks.hookOnce('page:finish', () => {
  updateHeadings(
    [
      document.querySelector('#features'),
      document.querySelector('#pricing'),
      document.querySelector('#testimonials')
    ].filter(Boolean) as Element[]
  )
})

const authStore = useAuthStore()
const { user, user_credit } = storeToRefs(authStore)
</script>

<template>
  <UHeader>
    <template #left>
      <NuxtLink to="/">
        <div class="flex flex-row gap-4 items-center mr-4">
          <BaseLargeLogo
            id="main-logo"
            :loading="loading"
            :size="`small`"
          />
          <BaseAppTitle
            class="justify-center text-center flex mx-auto !text-xl"
          />
        </div>
      </NuxtLink>
      <UNavigationMenu
        :items="items"
        variant="link"
        class="hidden lg:block"
      />
    </template>

    <template #right>
      <div class="flex flex-row gap-2 items-center">
        <div
          v-if="user"
          class="flex flex-row gap-2 items-center"
        >
          <!-- <UButton
            color="primary"
            variant="soft"
            trailing-icon="ic:baseline-plus"
            size="sm"
            class="hidden sm:block"
          >
            <div>
              {{ formatNumber(user_credit?.available_credit || 0) }}
              {{ $t("Credits") }}
            </div>
          </UButton> -->

          <!-- Language and Dark Mode Selectors -->
          <ULocaleSelect
            v-model="i18nLocale"
            :locales="[en, ja, vi, zh_cn, es, fr, de, pt]"
            variant="ghost"
            class="hidden sm:block"
            :ui="{
              content: 'w-40'
            }"
          />
          <BuyCreditsButton class="hidden sm:flex" />

          <ClientOnly>
            <UColorModeButton />
          </ClientOnly>

          <NotificationBell />
          <AppUserMenu />
        </div>
        <div
          v-else
          class="flex flex-row gap-2 items-center"
        >
          <!-- Language and Dark Mode Selectors -->
          <ULocaleSelect
            v-model="i18nLocale"
            :locales="[en, ja, vi, zh_cn, es, fr, de, pt]"
            variant="ghost"
            size="sm"
            class="rounded-full w-10 h-10 p-0"
            :ui="{
              content: 'w-40',
              base: 'rounded-full w-10 h-10 p-2 flex items-center justify-center',
              label: 'hidden',
              trailing: 'hidden'
            }"
          />
          <ClientOnly>
            <UColorModeSelect
              size="sm"
              variant="ghost"
              class="rounded-full w-10 h-10 p-0"
              :ui="{
                base: 'rounded-full w-10 h-10 p-2 flex items-center justify-center',
                label: 'hidden',
                trailing: 'hidden'
              }"
            />
            <template #fallback>
              <div
                class="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700"
              />
            </template>
          </ClientOnly>

          <UButton
            :label="$t('Login')"
            variant="subtle"
            color="neutral"
            class="hidden lg:block"
            to="/auth/login"
          />
          <UButton
            :label="$t('ui.buttons.signUp')"
            variant="solid"
            class="hidden lg:block"
            to="/auth/signup"
          />
        </div>
      </div>
    </template>

    <template #body>
      <UNavigationMenu
        :items="items"
        orientation="vertical"
        class="-mx-2.5"
      />
      <BuyCreditsButton class="mt-4" />
    </template>
  </UHeader>
</template>
